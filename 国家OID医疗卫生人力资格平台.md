---
title: 国家OID医疗卫生人力资格平台
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 国家OID医疗卫生人力资格平台

Base URLs:

# Authentication

# 登录模块

## POST 获取token接口

POST /loginInfo

> Body 请求参数

```json
{
  "username": "super",
  "password": "123456"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

# 人员登记

## GET 人员列表查询

GET /business/account/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNum|query|string| 否 |none|
|pageSize|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [
    {
      "createBy": null,
      "createTime": null,
      "updateBy": null,
      "updateTime": null,
      "remark": null,
      "id": 0,
      "oid": "string",
      "name": "string",
      "fileld": null,
      "phone": "string",
      "email": "string",
      "area": "string",
      "bron": "string",
      "seniority": "string",
      "post": "string",
      "notes": "string",
      "isusable": null
    }
  ],
  "code": 0,
  "msg": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» total|integer|true|none||none|
|» rows|[object]|true|none||none|
|»» createBy|null|true|none||none|
|»» createTime|null|true|none||none|
|»» updateBy|null|true|none||none|
|»» updateTime|null|true|none||none|
|»» remark|null|true|none||none|
|»» id|integer|true|none||none|
|»» oid|string|true|none||none|
|»» name|string|true|none|学员姓名|none|
|»» fileld|null|true|none|适用领域|none|
|»» phone|string¦null|true|none|手机|none|
|»» email|string¦null|true|none|邮箱|none|
|»» area|string¦null|true|none|常驻地|none|
|»» bron|string¦null|true|none|出生日期|none|
|»» seniority|string¦null|true|none|从业年限|none|
|»» post|string¦null|true|none|岗位|none|
|»» notes|string¦null|true|none|简历/简介|none|
|»» isusable|null|true|none|可用标识|none|
|» code|integer|true|none||none|
|» msg|string|true|none||none|

## POST 人员单次新增

POST /business/account

> Body 请求参数

```json
{
  "id": null,
  "oid": "1.2.156.3086.R1000000055",
  "name": "测试085",
  "fileld": "1",
  "phone": "1",
  "email": "1",
  "area": null,
  "bron": "2015-08-12",
  "seniority": null,
  "post": null,
  "notes": null,
  "isusable": null
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## PUT 人员修改

PUT /business/account

> Body 请求参数

```json
{
  "oid": "USER001",
  "name": "测试001",
  "fileld": "技术培训",
  "phone": "***********",
  "email": "<EMAIL>",
  "area": "北京",
  "bron": "1990-01-02",
  "seniority": "10年",
  "post": "高级培训师",
  "notes": "资深技术培训师，擅长Java开发培训",
  "isusable": 1
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## POST 批量新增

POST /business/account/batch

> Body 请求参数

```json
[
  {
    "oid": "USER001",
    "name": "测试001",
    "fileld": "技术培训",
    "phone": "***********",
    "email": "<EMAIL>",
    "area": "北京",
    "bron": "1990-01-01",
    "seniority": "5年",
    "post": "高级培训师",
    "notes": "资深技术培训师，擅长Java开发培训",
    "isusable": 1
  },
  {
    "oid": "USER002",
    "name": "测试002",
    "fileld": "管理培训",
    "phone": "***********",
    "email": "<EMAIL>",
    "area": "上海",
    "bron": "1985-05-15",
    "seniority": "8年",
    "post": "资深培训师",
    "notes": "专业管理培训师，擅长团队管理培训",
    "isusable": 1
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## DELETE 批量删除人员

DELETE /business/account/10,11

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

# 课程培训登记

## GET 课程列表查询

GET /business/course/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNum|query|string| 否 |none|
|pageSize|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [
    {
      "createBy": null,
      "createTime": null,
      "updateBy": null,
      "updateTime": null,
      "remark": null,
      "id": 0,
      "oid": "string",
      "name": "string",
      "createdate": null,
      "startdate": "string",
      "enddate": "string",
      "occupational": "string",
      "instoid": "string",
      "instname": "string",
      "traineroid": "string",
      "trainername": "string",
      "format": "string",
      "area": "string",
      "field": "string",
      "enrollment": 0,
      "isusable": 0
    }
  ],
  "code": 0,
  "msg": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» total|integer|true|none||none|
|» rows|[object]|true|none||none|
|»» createBy|null|true|none||none|
|»» createTime|null|true|none||none|
|»» updateBy|null|true|none||none|
|»» updateTime|null|true|none||none|
|»» remark|null|true|none||none|
|»» id|integer|true|none||none|
|»» oid|string|true|none||none|
|»» name|string|true|none|课程名称|none|
|»» createdate|null|true|none||none|
|»» startdate|string|true|none|培训开始日期|none|
|»» enddate|string|true|none|培训结束日期|none|
|»» occupational|string|true|none|从业资格|none|
|»» instoid|string|true|none|培训机构OID|none|
|»» instname|string¦null|true|none|培训机构名称|none|
|»» traineroid|string|true|none|培训师OID|none|
|»» trainername|string|true|none|培训师名称|none|
|»» format|string¦null|true|none|授课形式|none|
|»» area|string¦null|true|none|适用区域|none|
|»» field|string¦null|true|none|适用领域|none|
|»» enrollment|integer|true|none|参与人数|none|
|»» isusable|integer|true|none|开放状态|0为关闭  1为开放|
|» code|integer|true|none||none|
|» msg|string|true|none||none|

## POST 课程新增接口

POST /business/course

> Body 请求参数

```json
{
  "id": null,
  "oid": "1.2.156.3086.********.JY240401201111",
  "name": "1",
  "createdate": null,
  "startdate": "2025-08-05",
  "enddate": "2025-08-11",
  "occupational": "1",
  "instoid": "1.2.156.3086.********",
  "instname": "测试机构",
  "traineroid": "1.2.156.3086.R1000000001",
  "trainername": "测试培训师",
  "format": "1",
  "area": "1",
  "field": "1",
  "isusable": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## PUT 修改课程接口

PUT /business/course

> Body 请求参数

```json
{
  "id": 17,
  "oid": "1.2.156.3086.********.JY250805141314",
  "name": "测试0805课程",
  "createdate": null,
  "startdate": "2025-08-05",
  "enddate": "2025-08-11",
  "occupational": "1",
  "instoid": "1.2.156.3086.********",
  "instname": "测试机构",
  "traineroid": "1.2.156.3086.R1000000001",
  "trainername": "测试培训师",
  "format": "1",
  "area": "1",
  "field": "1",
  "isusable": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## POST 批量报名

POST /business/score/application/batch

{
  "courseoid": "1.2.156.3086.********.JY250805141314", 课程oid
  "accountoidList": [
    "USER001",用户oid'
    "USER002"
  ]
}

> Body 请求参数

```json
{
  "courseoid": "1.2.156.3086.********.JY250805141314",
  "accountoidList": [
    "USER001",
    "USER002"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## POST 批量取消报名

POST /business/score/undoapplication/batch

> Body 请求参数

```json
{
  "courseoid": "1.2.156.3086.********.JY250805141314",
  "accountoidList": [
    "USER001",
    "USER002"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

# 数据模型

